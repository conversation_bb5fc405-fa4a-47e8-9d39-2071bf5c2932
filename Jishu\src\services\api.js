import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_URL, AUTH_CONFIG } from '../config/index';
import { store } from '../store';

// The API_URL should be updated in the config file whenever your IP address changes
// The IP address will be displayed in the console when you run the Flask backend

// Mock users for development
const mockUsers = [
  {
    id: 1,
    mobile_number: '9876543210',
    name: 'Demo User',
    avatar: null,
    is_profile_complete: true,
    created_at: new Date().toISOString()
  }
];

// Create a function to get the stored token
const getToken = async () => {
  try {
    const token = await AsyncStorage.getItem('accessToken');
    return token;
  } catch (error) {
    console.error('Error getting token:', error);
    return null;
  }
};

// Helper function to check if a user exists
const isExistingUser = async (mobileNumber) => {
  try {
    // Check with the API
    const response = await fetch(`${API_URL}/api/auth/check-user?mobile=${mobileNumber}`);
    const data = await response.json();
    return data.exists || false;
  } catch (error) {
    console.error('Error checking if user exists:', error);
    return false;
  }
};

// Helper function for making API requests
const apiRequest = async (endpoint, method = 'GET', data = null, customHeaders = {}, requiresAuth = true) => {
  try {
    const url = `${API_URL}${endpoint}`;
    console.log(`Making ${method} request to: ${url}`);

    const headers = {
      'Content-Type': 'application/json',
      ...customHeaders
    };

    // Add authorization header if required
    if (requiresAuth) {
      const token = await getToken();
      if (token) {
        headers['Authorization'] = `JWT ${token}`;
      } else {
        // For development, we'll continue without token
        console.warn('Authentication required but no token found. Continuing for development.');
        // Return empty data for chat endpoints if no token is found
        if (endpoint.includes('/chat/')) {
          console.log('Chat endpoint requires authentication. Returning empty data.');
          if (endpoint === '/api/chat/personal') {
            return [];
          }
          return {};
        }
      }
    }

    const options = {
      method,
      headers,
    };

    if (data) {
      options.body = JSON.stringify(data);
      console.log('Request data:', data);
    }

    console.log('Sending request with options:', options);

    try {
      const response = await fetch(url, options);

      // Check if the response is JSON
      const contentType = response.headers.get('content-type');

      // Special handling for user exams endpoint
      if (endpoint.includes('/user/exams') && !response.ok) {
        console.log('User exams endpoint returned error, returning empty array');
        return [];
      }

      // Special handling for chat endpoints
      if (endpoint.includes('/chat/') && (response.status === 401 || response.status === 422)) {
        console.log('Chat endpoint returned auth error, returning empty data');
        if (endpoint === '/api/chat/personal' || endpoint.includes('/messages')) {
          return [];
        }
        return {};
      }

      if (!contentType || !contentType.includes('application/json')) {
        // Not JSON, try to get the text response for better error messages
        const textResponse = await response.text();
        console.error('Non-JSON response:', textResponse);

        // For user exams endpoint, return empty array instead of throwing
        if (endpoint.includes('/user/exams')) {
          console.log('User exams endpoint returned non-JSON, returning empty array');
          return [];
        }

        throw new Error(`Server returned non-JSON response: ${textResponse.substring(0, 100)}...`);
      }

      const result = await response.json();
      console.log('Response:', result);

      if (!response.ok) {
        // For user exams endpoint, return empty array instead of throwing
        if (endpoint.includes('/user/exams')) {
          console.log('User exams endpoint returned error, returning empty array');
          return [];
        }

        throw new Error(result.error || result.msg || 'API request failed');
      }

      return result;
    } catch (fetchError) {
      console.error(`Fetch error (${endpoint}):`, fetchError);

      // Handle network errors gracefully
      if (fetchError.message && fetchError.message.includes('Network request failed')) {
        console.error('Network request failed. The backend server may not be running.');

        // For authentication endpoints, throw a specific error
        if (endpoint.includes('/api/auth/')) {
          throw new Error('Authentication server is not available. Please make sure the backend is running.');
        }

        // For exam categories endpoints, return empty data
        if (endpoint.includes('/exam-categories') || endpoint.includes('/exams/categories')) {
          console.log('Returning empty array for exam categories due to network error');
          return [];
        }

        // For subjects endpoints, return empty array
        if (endpoint.includes('/subjects')) {
          console.log('Returning empty array for subjects due to network error');
          return [];
        }
      }

      // Handle 404 errors for specific endpoints
      if (fetchError.message && fetchError.message.includes('404')) {
        console.error('Endpoint not found (404):', endpoint);

        // For exam categories endpoints, return empty array
        if (endpoint.includes('/exam-categories') || endpoint.includes('/exams/categories')) {
          console.log('Returning empty array for exam categories due to 404 error');
          return [];
        }
      }

      // For user exams endpoints, return empty array
      if (endpoint.includes('/user/exams')) {
        console.log('Returning empty array for user exams due to network error');
        return [];
      }

      // For questions endpoints, return empty array
      if (endpoint.includes('/questions/subject')) {
        console.log('Returning empty array for questions due to network error');
        return [];
      }

      // For chat endpoints, return empty data
      if (endpoint.includes('/chat/')) {
        console.log('Returning empty data for chat endpoint due to network error');
        if (endpoint === '/api/chat/personal' || endpoint.includes('/messages')) {
          return [];
        }
        return {};
      }

      // For community endpoints, return empty data
      if (endpoint.includes('/community/posts')) {
        console.log('Returning empty data for community posts due to network error');

        if (method === 'GET') {
          if (endpoint.includes('/comments')) {
            return [];
          } else if (endpoint.includes('?page=')) {
            return { posts: [], total: 0, page: 1, per_page: 10, total_pages: 0 };
          } else if (endpoint.match(/\/community\/posts\/\d+$/)) {
            return { post: null, comments: [] };
          } else {
            return [];
          }
        } else if (method === 'POST') {
          if (endpoint === '/api/community/posts') {
            throw new Error('Failed to create post. Network error.');
          } else if (endpoint.includes('/comments')) {
            throw new Error('Failed to add comment. Network error.');
          } else if (endpoint.includes('/like')) {
            throw new Error('Failed to like post. Network error.');
          }
        }
      }

      // For authentication endpoints, we need to handle them specially
      if (endpoint.includes('/api/auth/')) {
        console.error('Authentication endpoint error:', fetchError.message);
        throw new Error('Authentication server is not available. Please make sure the backend is running.');
      }

      // For comment like endpoints, return empty data
      if (endpoint.includes('/community/comments') && endpoint.includes('/like')) {
        console.log('Returning empty data for comment like due to network error');
        throw new Error('Failed to like comment. Network error.');
      }

      throw fetchError;
    }
  } catch (error) {
    console.error(`API Error (${endpoint}):`, error);
    throw error;
  }
};

// Authentication API
export const authAPI = {
  // Request OTP
  requestOTP: (mobileNumber) => {
    console.log(`Requesting OTP for mobile number: ${mobileNumber}`);
    return apiRequest('/api/auth/request-otp', 'POST', { mobile_number: mobileNumber }, {}, false);
  },

  // Verify OTP and login
  verifyOTP: (mobileNumber, otp) => {
    console.log(`Verifying OTP for mobile number: ${mobileNumber}`);
    return apiRequest('/api/auth/verify-otp', 'POST', { mobile_number: mobileNumber, otp }, {}, false);
  },

  // Login with mobile number and OTP (two-step process)
  login: async (mobileNumber, otp = '123456') => {
    console.log(`Login attempt with mobile: ${mobileNumber}, OTP: ${otp}`);

    try {
      // First request OTP
      const otpResponse = await authAPI.requestOTP(mobileNumber);
      console.log('OTP request response:', otpResponse);

      // Then verify OTP
      const verifyResponse = await authAPI.verifyOTP(mobileNumber, otp);
      console.log('OTP verification response:', verifyResponse);

      if (verifyResponse && verifyResponse.success) {
        // Store tokens in AsyncStorage for persistence
        if (verifyResponse.access_token) {
          await AsyncStorage.setItem('accessToken', verifyResponse.access_token);
        }
        if (verifyResponse.refresh_token) {
          await AsyncStorage.setItem('refreshToken', verifyResponse.refresh_token);
        }

        return {
          success: true,
          message: 'Login successful',
          token: verifyResponse.access_token,
          user: verifyResponse.user,
          isNewUser: verifyResponse.is_new_user
        };
      } else {
        throw new Error(verifyResponse?.message || 'Login failed');
      }
    } catch (error) {
      console.error('API login failed:', error.message);
      throw error; // Let the caller handle the error
    }
  },

  // Register a new user (set username and avatar after OTP verification)
  register: async (mobileNumber, username, avatar) => {
    console.log(`Registering user: ${username}, mobile: ${mobileNumber}, avatar: ${avatar}`);

    try {
      // Call the API to complete the user profile
      const response = await apiRequest('/api/auth/complete-profile', 'POST', {
        mobile_number: mobileNumber,
        name: username,
        avatar
      }, {}, false);

      console.log('Register API response:', response);

      if (response && response.success) {
        return response;
      } else {
        throw new Error(response?.message || 'Registration failed');
      }
    } catch (error) {
      console.error('API registration failed:', error.message);
      throw error; // Let the caller handle the error
    }
  },

  // Refresh token
  refreshToken: async () => {
    try {
      const refreshToken = await AsyncStorage.getItem('refreshToken');
      if (!refreshToken) throw new Error('No refresh token found');

      const response = await fetch(`${API_URL}/api/auth/refresh`, {
        method: 'POST',
        headers: {
          'Authorization': `JWT ${refreshToken}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();
      if (!response.ok) throw new Error(data.error || 'Failed to refresh token');

      // Save the new access token
      await AsyncStorage.setItem('accessToken', data.access_token);
      return data.access_token;
    } catch (error) {
      console.error('Error refreshing token:', error);
      throw error;
    }
  },

  // Get current user info
  getCurrentUser: () => {
    return apiRequest('/api/auth/me', 'GET');
  },

  // Update user profile (username, avatar, etc.)
  updateProfile: (profileData) => {
    return apiRequest('/api/auth/update-profile', 'POST', profileData);
  },

  // Complete user profile (name, avatar) - used after first login
  completeProfile: (profileData) => {
    console.log('Completing user profile with data:', profileData);
    // Don't require authentication for this endpoint since it might be called right after login
    return apiRequest('/api/auth/complete-profile', 'POST', profileData, {}, false);
  },

  // Logout
  logout: async () => {
    try {
      // Only remove tokens from AsyncStorage
      await AsyncStorage.removeItem('accessToken');
      await AsyncStorage.removeItem('refreshToken');
      return true;
    } catch (error) {
      console.error('Error during logout:', error);
      throw error;
    }
  }
};

// Exam Categories API
export const examCategoriesAPI = {
  // Get all exam categories
  getAll: async () => {
    try {
      console.log('Attempting to fetch exam categories from primary endpoint...');
      try {
        // Try the first endpoint
        const response = await apiRequest('/api/exams/categories', 'GET');
        console.log('Exam categories API response from primary endpoint:', response);
        if (Array.isArray(response) && response.length > 0) {
          return response;
        }
      } catch (primaryError) {
        console.error('Error fetching from primary endpoint:', primaryError);
        // Continue to fallback endpoint
      }

      console.log('Attempting to fetch exam categories from fallback endpoint...');
      try {
        // Try the fallback endpoint
        const fallbackResponse = await apiRequest('/api/exam-categories', 'GET');
        console.log('Exam categories API response from fallback endpoint:', fallbackResponse);
        if (Array.isArray(fallbackResponse) && fallbackResponse.length > 0) {
          return fallbackResponse;
        }
      } catch (fallbackError) {
        console.error('Error fetching from fallback endpoint:', fallbackError);
        // Only use mock data if both API endpoints fail
      }

      // If both endpoints fail, log an error and return an empty array
      console.log('Both endpoints failed, returning empty array');
      return [];
    } catch (error) {
      console.error('Error fetching exam categories from all endpoints:', error);
      return [];
    }
  },

  // Get a specific exam category
  getById: async (id) => {
    try {
      console.log(`Attempting to fetch exam category with id ${id} from primary endpoint...`);
      try {
        // Try the first endpoint
        const response = await apiRequest(`/api/exams/categories/${id}`, 'GET');
        if (response) {
          return response;
        }
      } catch (primaryError) {
        console.error(`Error fetching category ${id} from primary endpoint:`, primaryError);
        // Continue to fallback endpoint
      }

      console.log(`Attempting to fetch exam category with id ${id} from fallback endpoint...`);
      try {
        // Try the fallback endpoint
        const fallbackResponse = await apiRequest(`/api/exam-categories/${id}`, 'GET');
        if (fallbackResponse) {
          return fallbackResponse;
        }
      } catch (fallbackError) {
        console.error(`Error fetching category ${id} from fallback endpoint:`, fallbackError);
      }

      // If both endpoints fail, return null
      console.log(`Both endpoints failed, returning null for category ${id}`);
      return null;
    } catch (error) {
      console.error(`Error fetching exam category with id ${id} from all endpoints:`, error);
      return null;
    }
  }
};

// Subjects API
export const subjectsAPI = {
  // Get all subjects (optionally filtered by category)
  getAll: async (categoryId = null) => {
    try {
      const endpoint = categoryId ? `/api/subjects?category_id=${categoryId}` : '/api/subjects';
      const response = await apiRequest(endpoint, 'GET');
      console.log('Subjects API response:', response);
      return response || [];
    } catch (error) {
      console.error('Error fetching subjects:', error);
      return []; // Return empty array on error
    }
  },

  // Get a specific subject
  getById: async (id) => {
    try {
      const response = await apiRequest(`/api/subjects/${id}`, 'GET');
      return response;
    } catch (error) {
      console.error(`Error fetching subject with id ${id}:`, error);
      throw error; // Let the caller handle the error
    }
  },

  // Get subjects by category
  getByCategory: async (categoryId) => {
    try {
      console.log(`Fetching subjects for category ID: ${categoryId}`);

      try {
        const response = await apiRequest(`/api/subjects/category/${categoryId}`, 'GET');
        console.log('Subjects by category API response:', response);

        // Format the response to match the expected structure
        if (Array.isArray(response) && response.length > 0) {
          return response.map(subject => ({
            id: subject.id,
            name: subject.name,
            description: subject.description,
            category_id: subject.category_id,
            category_name: subject.category_name,
            max_retakes: subject.max_retakes || 3,
            question_count: subject.question_count || 50
          }));
        }
      } catch (apiError) {
        console.error(`API error fetching subjects for category ${categoryId}:`, apiError);
      }

      // If API fails, return empty array
      console.log(`API failed, returning empty array for category ${categoryId}`);
      return [];
    } catch (error) {
      console.error(`Error fetching subjects for category ${categoryId}:`, error);
      return [];
    }
  }
};

// User Exams API
export const userExamsAPI = {
  // Get all exams purchased by the user
  getAll: async () => {
    // Get current user data from Redux store if available
    const state = store.getState();
    const user = state.auth && state.auth.user;
    const userId = user?.id || 1;

    console.log('Fetching purchased exams for user:', userId);

    try {
      // Try to get from the real API first
      console.log('Fetching exams from API...');
      try {
        const apiExams = await apiRequest('/api/user/exams', 'GET');

        // If we get a valid array response
        if (Array.isArray(apiExams)) {
          console.log(`Fetched ${apiExams.length} exams from API for user ${userId}`);

          // Save to AsyncStorage for offline access
          if (apiExams.length > 0) {
            await AsyncStorage.setItem('userPurchasedExams', JSON.stringify(apiExams));
            console.log('Saved API exams to AsyncStorage');
          }
          return apiExams;
        } else {
          console.log('API returned non-array response for user exams:', apiExams);
          // Continue to fallback methods
        }
      } catch (apiError) {
        console.error('API error fetching user exams:', apiError);
        // Continue to fallback methods
      }

      // If API returns empty or fails, try to get from AsyncStorage
      const storedExamsJson = await AsyncStorage.getItem('userPurchasedExams');
      if (storedExamsJson) {
        try {
          const storedExams = JSON.parse(storedExamsJson);
          console.log(`Found ${storedExams.length} purchased exams in AsyncStorage`);

          // Filter by current user if we have a user ID
          if (userId) {
            const filteredExams = storedExams.filter(exam => exam.user_id === userId);
            console.log(`Filtered to ${filteredExams.length} exams for user ${userId}`);
            return filteredExams;
          }

          return storedExams;
        } catch (parseError) {
          console.error('Error parsing stored exams:', parseError);
        }
      }

      // Return empty array instead of mock data
      console.log('No data found, returning empty array');
      return [];
    } catch (error) {
      console.error('Error fetching user exams:', error);

      // Try to get from AsyncStorage if API fails
      try {
        const storedExamsJson = await AsyncStorage.getItem('userPurchasedExams');
        if (storedExamsJson) {
          const storedExams = JSON.parse(storedExamsJson);
          console.log(`Falling back to ${storedExams.length} stored exams from AsyncStorage`);
          return storedExams;
        }
      } catch (storageError) {
        console.error('Error reading from AsyncStorage:', storageError);
      }

      // If all else fails, return empty array
      console.log('All fallbacks failed, returning empty array');
      return [];
    }
  },

  // Purchase an exam
  purchaseExam: async (subjectId, examData = null) => {
    // Ensure subjectId is valid
    if (subjectId === null || subjectId === undefined) {
      console.error('Invalid subject ID: null or undefined');
      return Promise.reject(new Error('Subject ID is required'));
    }

    // Convert to number for API call
    const numericSubjectId = typeof subjectId === 'string' ? parseInt(subjectId, 10) : subjectId;
    console.log(`Purchasing exam with subject ID: ${numericSubjectId}, Type: ${typeof numericSubjectId}`);

    // Validate that we have a valid number
    if (isNaN(numericSubjectId)) {
      console.error('Invalid subject ID: not a number after conversion');
      return Promise.reject(new Error('Invalid subject ID'));
    }

    try {
      console.log(`Attempting API purchase for subject ID: ${numericSubjectId}`);
      // Send the subject_id in the request body
      const apiResponse = await apiRequest('/api/user/exams/purchase', 'POST', {
        subject_id: numericSubjectId,
        max_retakes: examData?.max_retakes || 3,
        marks: examData?.marks || 4,
        negative_marks: examData?.negative_marks || 1
      });

      console.log('Purchase API response:', apiResponse);

      // If successful, update AsyncStorage for offline access
      if (apiResponse && apiResponse.exam) {
        try {
          // Update userPurchasedExams in AsyncStorage
          const storedExamsJson = await AsyncStorage.getItem('userPurchasedExams');
          let storedExams = [];

          if (storedExamsJson) {
            storedExams = JSON.parse(storedExamsJson);
          }

          // Add the new purchase if it doesn't exist
          const existingIndex = storedExams.findIndex(
            exam => exam.subject_id === numericSubjectId
          );

          if (existingIndex >= 0) {
            // Update existing exam
            storedExams[existingIndex] = {
              ...storedExams[existingIndex],
              ...apiResponse.exam
            };
          } else {
            // Add new exam
            storedExams.push(apiResponse.exam);
          }

          await AsyncStorage.setItem('userPurchasedExams', JSON.stringify(storedExams));
          console.log('Updated purchased exams in AsyncStorage');

          // Also update subscribedExams for backward compatibility
          await userExamsAPI.updateSubscribedExams(apiResponse.exam, examData);
        } catch (storageError) {
          console.error('Error updating AsyncStorage:', storageError);
        }
      }

      return apiResponse;
    } catch (error) {
      console.error(`API purchase failed: ${error.message}`);

      // Check if the user already has this exam purchased
      try {
        const userExams = await userExamsAPI.getAll();
        const existingExam = userExams.find(exam =>
          exam.subject_id === numericSubjectId ||
          (exam.subject && exam.subject.id === numericSubjectId)
        );

        if (existingExam) {
          console.log('User already has this exam purchased:', existingExam);
          return {
            message: 'Exam already purchased',
            exam: existingExam
          };
        }
      } catch (checkError) {
        console.error('Error checking existing purchases:', checkError);
      }

      // Return error instead of mock data
      console.log('API purchase failed, returning error');
      return Promise.reject(new Error('Failed to purchase exam. Please try again later.'));
    }
  },

  // For backward compatibility
  purchase: async (subjectId, examData = null) => {
    console.log('Using purchaseExam instead of purchase');
    return userExamsAPI.purchaseExam(subjectId, examData);
  },

  // Helper function to update subscribedExams in AsyncStorage
  updateSubscribedExams: async (exam, examData) => {
    try {
      const subscribedExamsJson = await AsyncStorage.getItem('subscribedExams');
      let subscribedExams = [];

      if (subscribedExamsJson) {
        subscribedExams = JSON.parse(subscribedExamsJson);
      }

      // Format the exam for the old format
      const formattedExam = {
        id: exam.id.toString(),
        title: exam.subject.name,
        description: exam.subject.description || 'Review your performance',
        score: 0,
        totalQuestions: examData?.questions || 50,
        lastAttemptDate: null,
        attemptCount: 0,
        subjectId: exam.subject_id,
        category: examData?.category || 'NEET',
        categoryName: examData?.categoryName || 'NEET',
        purchasedAt: exam.purchased_at
      };

      // Add to subscribed exams
      subscribedExams.push(formattedExam);
      await AsyncStorage.setItem('subscribedExams', JSON.stringify(subscribedExams));
    } catch (error) {
      console.error('Error updating subscribedExams:', error);
    }
  },

  // Start an exam attempt
  startExam: (userExamId) => {
    // Ensure userExamId is a string
    const userExamIdParam = typeof userExamId === 'string' ? userExamId : String(userExamId);
    return apiRequest(`/api/user/exams/${userExamIdParam}/start`, 'POST');
  }
};

// Exam Attempts API
export const examAttemptsAPI = {
  // Get all attempts by the user
  getAll: () => {
    console.log('Fetching all user exam attempts');
    try {
      return apiRequest('/api/user/attempts', 'GET');
    } catch (error) {
      console.error('Error fetching user attempts:', error.message);
      // Fallback to mock data if API fails
      console.log('Falling back to mock attempts data');
      return Promise.resolve([]);
    }
  },

  // Get a specific attempt
  getById: (attemptId) => {
    console.log(`Fetching attempt with id ${attemptId}`);
    try {
      return apiRequest(`/api/user/attempts/${attemptId}`, 'GET');
    } catch (error) {
      console.error(`Error fetching attempt: ${error.message}`);
      // Fallback to mock data if API fails
      console.log(`Falling back to mock attempt data for id ${attemptId}`);
      return Promise.resolve({
        id: attemptId,
        score: 0,
        total_questions: 50,
        correct_answers: 0,
        wrong_answers: 0,
        unattempted: 50,
        time_taken_seconds: 0,
        started_at: new Date().toISOString(),
        completed_at: null
      });
    }
  },

  // Start an exam attempt
  startExam: (userExamId) => {
    console.log(`Starting exam attempt for user exam id ${userExamId}`);
    try {
      return apiRequest(`/api/user/exams/${userExamId}/start`, 'POST');
    } catch (error) {
      console.error(`Error starting exam: ${error.message}`);
      // Fallback to mock data if API fails
      console.log(`Falling back to mock start exam data for user exam id ${userExamId}`);
      return Promise.resolve({
        id: Date.now(), // Use timestamp as mock attempt ID
        user_exam_id: userExamId,
        started_at: new Date().toISOString(),
        completed_at: null
      });
    }
  },

  // Submit an exam attempt
  submitExam: (attemptId, answers, timeTakenSeconds) => {
    console.log(`Submitting exam attempt ${attemptId}`);
    console.log('Answers:', answers);
    console.log('Time taken:', timeTakenSeconds);

    try {
      return apiRequest(`/api/user/attempts/${attemptId}/submit`, 'POST', {
        answers,
        time_taken_seconds: timeTakenSeconds
      });
    } catch (error) {
      console.error(`Error submitting exam: ${error.message}`);
      // Fallback to mock data if API fails
      console.log(`Falling back to mock submit exam data for attempt id ${attemptId}`);

      // Calculate mock score
      const correctAnswers = Math.floor(Math.random() * answers.length);
      const wrongAnswers = answers.length - correctAnswers;
      const score = correctAnswers * 4 - wrongAnswers;

      return Promise.resolve({
        id: attemptId,
        score: score,
        total_questions: answers.length + Math.floor(Math.random() * 10), // Add some unattempted questions
        correct_answers: correctAnswers,
        wrong_answers: wrongAnswers,
        unattempted: Math.floor(Math.random() * 10),
        time_taken_seconds: timeTakenSeconds,
        started_at: new Date(Date.now() - timeTakenSeconds * 1000).toISOString(),
        completed_at: new Date().toISOString()
      });
    }
  }
};

// Questions API
export const questionsAPI = {
  // Get questions for an exam
  getForExam: async (subjectId, count = 50) => {
    console.log(`Fetching questions for subject ${subjectId}, count: ${count}`);
    try {
      const response = await apiRequest(`/api/questions/subject/${subjectId}?count=${count}`, 'GET');
      console.log(`Fetched ${response.length} questions for subject ${subjectId}`);

      // Format the response to match the expected structure
      if (Array.isArray(response)) {
        return response.map(question => ({
          id: question.id,
          subject_id: question.subject_id,
          text: question.text,
          option_a: question.option_a,
          option_b: question.option_b,
          option_c: question.option_c,
          option_d: question.option_d,
          correct_option: question.correct_option,
          explanation: question.explanation
        }));
      }

      return [];
    } catch (error) {
      console.error(`Error fetching questions: ${error.message}`);
      return []; // Return empty array on error
    }
  },

  // Get a specific question
  getById: async (id) => {
    console.log(`Fetching question with id ${id}`);
    try {
      const response = await apiRequest(`/api/questions/${id}`, 'GET');
      return response;
    } catch (error) {
      console.error(`Error fetching question: ${error.message}`);
      return null;
    }
  }
};

// Mock API for development when backend is not available
export const mockAPI = {
  // Mock exam categories
  getExamCategories: async () => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Return empty array instead of mock data
    return [];
  },

  // Mock community posts
  getCommunityPosts: async (page = 1, perPage = 10) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Sample avatar images
    const avatars = [
      'https://randomuser.me/api/portraits/men/32.jpg',
      'https://randomuser.me/api/portraits/women/44.jpg',
      'https://randomuser.me/api/portraits/men/86.jpg',
      'https://randomuser.me/api/portraits/women/63.jpg',
      'https://randomuser.me/api/portraits/men/29.jpg',
    ];

    // Generate mock posts based on page number
    const posts = [];
    const startIndex = (page - 1) * perPage;
    const totalPosts = 25; // Total mock posts available

    for (let i = 0; i < perPage; i++) {
      const index = startIndex + i;
      if (index >= totalPosts) break;

      posts.push({
        id: index + 1,
        user_id: (index % 5) + 1,
        user_name: ['Priya Sharma', 'Rahul Verma', 'Ananya Patel', 'Arjun Kapoor', 'Vikram Mehta'][index % 5],
        user_avatar: avatars[index % 5],
        content: [
          "Just finished my NEET practice test! Scored 85%. The biology section was challenging but I managed to get through it. Any tips for improving in biology?",
          "Anyone else struggling with JEE Advanced mechanics problems? I'm finding it hard to visualize the force diagrams.",
          "Just got my CET results! So happy with my performance. The practice tests on this app really helped me prepare well.",
          "Looking for study partners for GATE CSE preparation. Anyone interested?",
          "How do you guys manage time during the JEE exam? I always run out of time in the math section."
        ][index % 5],
        likes_count: Math.floor(Math.random() * 50),
        comments_count: Math.floor(Math.random() * 10),
        created_at: new Date(Date.now() - Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000)).toISOString()
      });
    }

    return {
      posts,
      total: totalPosts,
      pages: Math.ceil(totalPosts / perPage),
      current_page: page
    };
  },

  // Mock community post comments
  getPostComments: async (postId) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800));

    // Sample avatar images
    const avatars = [
      'https://randomuser.me/api/portraits/men/32.jpg',
      'https://randomuser.me/api/portraits/women/44.jpg',
      'https://randomuser.me/api/portraits/men/86.jpg',
      'https://randomuser.me/api/portraits/women/63.jpg',
      'https://randomuser.me/api/portraits/men/29.jpg',
    ];

    // Generate 0-5 comments based on post ID
    const commentCount = (postId % 5) + 1;
    const comments = [];

    for (let i = 0; i < commentCount; i++) {
      comments.push({
        id: i + 1,
        post_id: postId,
        user_id: (i % 5) + 1,
        user_name: ['Rahul Verma', 'Ananya Patel', 'Vikram Mehta', 'Priya Sharma', 'Arjun Kapoor'][i % 5],
        user_avatar: avatars[i % 5],
        content: [
          "Great job! For biology, try focusing on diagrams and flowcharts. It helped me a lot.",
          "NCERT is the key for biology. Make sure you've covered it thoroughly.",
          "Try HC Verma's book. The examples are really helpful for visualization.",
          "Congratulations! What was your score?",
          "I'm interested! Let's form a study group."
        ][i % 5],
        likes_count: Math.floor(Math.random() * 10),
        created_at: new Date(Date.now() - Math.floor(Math.random() * 24 * 60 * 60 * 1000)).toISOString()
      });
    }

    return comments;
  },

  // Mock subjects by category - now returns empty array to match empty database
  getSubjectsByCategory: async (categoryId) => {
    console.log(`Mock getSubjectsByCategory called for category ${categoryId} - returning empty array`);
    return [];
  },

  // Mock user exams storage - empty array to avoid showing mock data
  _mockUserExams: [],

  // Add a new user exam to the mock data
  addUserExam: function(newExam) {
    console.log('Adding new user exam to mock data:', newExam);

    // Ensure we have a valid user_id
    if (!newExam.user_id) {
      const state = store.getState();
      newExam.user_id = (state.auth && state.auth.user && state.auth.user.id) || 1;
    }

    // Ensure subject_id is a string
    if (newExam.subject_id !== undefined && newExam.subject_id !== null) {
      newExam.subject_id = String(newExam.subject_id);
    }

    // Check if exam already exists for this user and subject
    const existingIndex = this._mockUserExams.findIndex(
      exam => exam.user_id === newExam.user_id &&
              String(exam.subject_id) === String(newExam.subject_id)
    );

    if (existingIndex >= 0) {
      console.log('Exam already exists, updating:', this._mockUserExams[existingIndex]);
      this._mockUserExams[existingIndex] = {
        ...this._mockUserExams[existingIndex],
        ...newExam,
        purchase_count: this._mockUserExams[existingIndex].purchase_count + 1
      };
    } else {
      console.log('Adding new exam to mock data');
      this._mockUserExams.push(newExam);
    }

    console.log('Updated mock user exams:', this._mockUserExams);
    return newExam;
  },

  // Get user exams
  getUserExams: async (userId = null) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log('Mock getUserExams called - returning empty array');
    return []; // Return empty array instead of mock data
  },

  // Mock questions for an exam
  getQuestionsForExam: async (subjectId, count = 50) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log('Mock getQuestionsForExam called - returning empty array');
    return []; // Return empty array instead of mock data
  }
};

// Community API
export const communityAPI = {
  // Get all posts with pagination
  getPosts: (page = 1, perPage = 10) => {
    return apiRequest(`/api/community/posts?page=${page}&per_page=${perPage}`, 'GET');
  },

  // Get a specific post with its comments
  getPost: (postId) => {
    return apiRequest(`/api/community/posts/${postId}`, 'GET');
  },

  // Get comments for a post
  getPostComments: (postId) => {
    console.log(`API Service: Getting comments for post ${postId}`);
    return apiRequest(`/api/community/posts/${postId}/comments`, 'GET')
      .then(response => {
        console.log(`API Service: Got ${response.length || 0} comments for post ${postId}:`, response);
        return response;
      })
      .catch(error => {
        console.error(`API Service: Error getting comments for post ${postId}:`, error);
        throw error;
      });
  },

  // Create a new post
  createPost: (content, title = '', userData = null) => {
    console.log('API Service: Creating post with content:', content);

    // Get current user data from Redux store if available
    const state = store.getState();
    const user = userData || (state.auth && state.auth.user) || {};

    // Create post data with user information
    const postData = {
      content,
      title,
      user_id: user.id,
      user_name: user.name || 'Guest'
    };

    console.log('API Service: Post data with user info:', postData);

    return apiRequest('/api/community/posts', 'POST', postData, {
      'X-User-ID': user.id,
      'X-User-Name': user.name || 'Guest'
    })
      .then(response => {
        console.log('API Service: Post created successfully:', response);
        return response;
      })
      .catch(error => {
        console.error('API Service: Error creating post:', error);
        throw error;
      });
  },

  // Add a comment to a post
  addComment: (postId, content, userData = null) => {
    console.log(`API Service: Adding comment to post ${postId} with content:`, content);

    // Get current user data from Redux store if available
    const state = store.getState();
    const user = userData || (state.auth && state.auth.user) || {};

    // Create comment data with user information
    const commentData = {
      content,
      user_id: user.id,
      user_name: user.name || 'Guest'
    };

    console.log(`API Service: Comment data with user info:`, commentData);

    return apiRequest(`/api/community/posts/${postId}/comments`, 'POST', commentData, {
      'X-User-ID': user.id,
      'X-User-Name': user.name || 'Guest'
    })
      .then(response => {
        console.log(`API Service: Comment added successfully to post ${postId}:`, response);
        return response;
      })
      .catch(error => {
        console.error(`API Service: Error adding comment to post ${postId}:`, error);
        throw error;
      });
  },

  // Like or unlike a post
  likePost: (postId) => {
    return apiRequest(`/api/community/posts/${postId}/like`, 'POST');
  },

  // Like or unlike a comment
  likeComment: (commentId) => {
    return apiRequest(`/api/community/comments/${commentId}/like`, 'POST');
  }
};

// Chat API for personal messaging
export const chatAPI = {
  // Get all personal chat conversations
  getPersonalChats: () => {
    console.log('Fetching personal chat conversations');
    return apiRequest('/api/chat/personal', 'GET');
  },

  // Get messages for a specific personal chat
  getPersonalChatMessages: (userId) => {
    console.log(`Fetching messages for chat with user ${userId}`);
    return apiRequest(`/api/chat/personal/${userId}/messages`, 'GET');
  },

  // Send a message in a personal chat
  sendPersonalChatMessage: (userId, message) => {
    console.log(`Sending message to user ${userId}: ${message}`);
    return apiRequest(`/api/chat/personal/${userId}/messages`, 'POST', {
      message
    });
  },

  // Get all chat sessions
  getChatSessions: () => {
    return apiRequest('/api/chat/sessions', 'GET');
  },

  // Get a specific chat session with messages
  getChatSession: (sessionId) => {
    return apiRequest(`/api/chat/sessions/${sessionId}`, 'GET');
  },

  // Create a new chat session
  createChatSession: (title, subjectId = null) => {
    return apiRequest('/api/chat/sessions', 'POST', {
      title,
      subject_id: subjectId
    });
  },

  // Add a message to a chat session
  addChatMessage: (sessionId, message) => {
    return apiRequest(`/api/chat/sessions/${sessionId}/messages`, 'POST', {
      message
    });
  }
};

// AI API
export const aiAPI = {
  // Send a message to the chatbot
  sendMessage: (question, chatHistory = []) => {
    console.log(`Sending message to chatbot: ${question}`);
    return apiRequest('/api/ai/chat', 'POST', {
      question,
      chat_history: chatHistory,
      timestamp: new Date().toISOString()
    });
  },

  // Generate MCQ questions for preview
  generateMCQPreview: (topic, subjectName = 'General', numQuestions = 3) => {
    console.log(`Generating MCQ preview for topic: ${topic}`);
    return apiRequest('/api/ai/generate-mcq-preview', 'POST', {
      topic,
      subject_name: subjectName,
      num_questions: numQuestions
    });
  },

  // Generate and store MCQ questions
  generateMCQ: (topic, subjectId, numQuestions = 5) => {
    console.log(`Generating MCQ for topic: ${topic}, subject: ${subjectId}`);
    return apiRequest('/api/ai/generate-mcq', 'POST', {
      topic,
      subject_id: subjectId,
      num_questions: numQuestions
    });
  },

  // Upload a PDF file for processing
  uploadPDF: async (file) => {
    console.log(`Uploading PDF: ${file.name}`);

    const formData = new FormData();
    formData.append('file', file);

    const token = await getToken();
    const headers = token ? { 'Authorization': `Bearer ${token}` } : {};

    try {
      const response = await fetch(`${API_URL}/api/ai/process-pdf`, {
        method: 'POST',
        headers,
        body: formData
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to upload PDF');
      }

      return result;
    } catch (error) {
      console.error('Error uploading PDF:', error);
      throw error;
    }
  },

  // Upload multiple PDF files for processing
  uploadPDFs: async (files) => {
    console.log(`Uploading ${files.length} PDFs`);

    const formData = new FormData();
    for (let i = 0; i < files.length; i++) {
      formData.append('files[]', files[i]);
    }

    const token = await getToken();
    const headers = token ? { 'Authorization': `Bearer ${token}` } : {};

    try {
      const response = await fetch(`${API_URL}/api/ai/process-pdfs`, {
        method: 'POST',
        headers,
        body: formData
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to upload PDFs');
      }

      return result;
    } catch (error) {
      console.error('Error uploading PDFs:', error);
      throw error;
    }
  }
};

export default {
  authAPI,
  examCategoriesAPI,
  subjectsAPI,
  userExamsAPI,
  examAttemptsAPI,
  questionsAPI,
  communityAPI,
  chatAPI,
  aiAPI,
  mockAPI
};
