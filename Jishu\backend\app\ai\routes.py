# from flask import request, jsonify
# from flask_jwt_extended import jwt_required, get_jwt_identity
# from app.ai import ai_bp
# from app.ai.services import AIService
# from app.models.exam import Subject
# import os
# import shutil
# from werkzeug.utils import secure_filename

# ai_service = AIService()

# # Create a directory for storing uploaded PDFs
# UPLOAD_FOLDER = os.path.join(os.getcwd(), 'uploads')
# if not os.path.exists(UPLOAD_FOLDER):
#     os.makedirs(UPLOAD_FOLDER)

# @ai_bp.route('/process-pdf', methods=['POST'])
# def process_pdf():
#     """Process a single PDF file and create embeddings"""
#     if 'file' not in request.files:
#         return jsonify({'error': 'No file provided'}), 400

#     file = request.files['file']
#     if file.filename == '':
#         return jsonify({'error': 'No file selected'}), 400

#     if not file.filename.endswith('.pdf'):
#         return jsonify({'error': 'File must be a PDF'}), 400

#     # Save the file temporarily
#     temp_path = f"temp_{secure_filename(file.filename)}"
#     file.save(temp_path)

#     try:
#         num_chunks = ai_service.process_pdf(temp_path)
#         return jsonify({
#             'message': 'PDF processed successfully',
#             'chunks': num_chunks
#         })
#     finally:
#         # Clean up temporary file
#         if os.path.exists(temp_path):
#             os.remove(temp_path)

# @ai_bp.route('/process-pdfs', methods=['POST'])
# def process_pdfs():
#     """Process multiple PDF files and create embeddings"""
#     if 'files[]' not in request.files:
#         return jsonify({'error': 'No files provided'}), 400

#     files = request.files.getlist('files[]')
#     if not files or len(files) == 0:
#         return jsonify({'error': 'No files selected'}), 400

#     # Create a temporary directory for the uploaded files
#     temp_dir = os.path.join(UPLOAD_FOLDER, 'temp_batch')
#     if os.path.exists(temp_dir):
#         shutil.rmtree(temp_dir)
#     os.makedirs(temp_dir)

#     try:
#         # Save all files to the temporary directory
#         for file in files:
#             if file.filename == '':
#                 continue

#             if not file.filename.endswith('.pdf'):
#                 continue

#             file_path = os.path.join(temp_dir, secure_filename(file.filename))
#             file.save(file_path)

#         # Process all PDFs in the directory
#         num_chunks = ai_service.process_pdf_directory(temp_dir)

#         return jsonify({
#             'message': f'Successfully processed {len(files)} PDF files',
#             'chunks': num_chunks
#         })
#     except Exception as e:
#         return jsonify({'error': str(e)}), 500
#     finally:
#         # Clean up temporary directory
#         if os.path.exists(temp_dir):
#             shutil.rmtree(temp_dir)

# @ai_bp.route('/chat', methods=['POST'])
# def chat():
#     """Get a response from the chatbot using both general knowledge and PDF content"""
#     data = request.get_json() or {}

#     if 'question' not in data:
#         return jsonify({'error': 'Question is required'}), 400

#     try:
#         # Get chat history from request
#         chat_history = data.get('chat_history', [])

#         # Get user ID if available
#         user_id = None
#         if request.headers.get('Authorization'):
#             try:
#                 user_id = get_jwt_identity()
#             except:
#                 # Continue without user ID
#                 pass

#         # Get response from AI service
#         response = ai_service.get_chat_response(data['question'], chat_history)

#         # Create response object
#         response_data = {
#             'response': response,
#             'timestamp': data.get('timestamp', None)
#         }

#         # Add source documents if available
#         if hasattr(response, 'get') and response.get('source_documents'):
#             sources = []
#             for doc in response.get('source_documents', []):
#                 if hasattr(doc, 'metadata'):
#                     sources.append({
#                         'source': doc.metadata.get('source', 'Unknown'),
#                         'page': doc.metadata.get('page', 0)
#                     })
#             response_data['sources'] = sources

#         return jsonify(response_data)
#     except ValueError as e:
#         return jsonify({'error': str(e)}), 400
#     except Exception as e:
#         return jsonify({'error': f'An error occurred: {str(e)}'}), 500

# @ai_bp.route('/generate-mcq', methods=['POST'])
# @jwt_required()
# def generate_mcq():
#     """Generate MCQ questions for a given topic and store them"""
#     data = request.get_json() or {}

#     if not all(k in data for k in ('topic', 'subject_id')):
#         return jsonify({'error': 'Topic and subject_id are required'}), 400

#     try:
#         user_id = get_jwt_identity()
#         num_questions = data.get('num_questions', 5)

#         # Check if subject exists
#         subject = Subject.query.get(data['subject_id'])
#         if not subject:
#             return jsonify({'error': 'Invalid subject ID'}), 404

#         # Generate and store questions
#         questions = ai_service.generate_and_store_mcq(
#             topic=data['topic'],
#             subject_id=data['subject_id'],
#             user_id=user_id,
#             num_questions=num_questions
#         )

#         return jsonify({
#             'message': f'Successfully generated {len(questions)} questions',
#             'questions': [q.to_dict() for q in questions]
#         })

#     except ValueError as e:
#         return jsonify({'error': str(e)}), 400
#     except Exception as e:
#         print(f"Error generating MCQ questions: {e}")
#         return jsonify({'error': f'Failed to generate questions: {str(e)}'}), 500

# @ai_bp.route('/generate-mcq-preview', methods=['POST'])
# def generate_mcq_preview():
#     """Generate MCQ questions for preview without storing them"""
#     data = request.get_json() or {}

#     if 'topic' not in data:
#         return jsonify({'error': 'Topic is required'}), 400

#     try:
#         num_questions = data.get('num_questions', 3)
#         subject_name = data.get('subject_name', 'General')

#         # Generate MCQs without storing them
#         if ai_service.vector_store:
#             mcq_json = ai_service.generate_mcq(data['topic'], num_questions)
#         else:
#             mcq_json = ai_service.generate_general_mcq(data['topic'], subject_name, num_questions)

#         # Parse the response as JSON
#         questions_data = json.loads(mcq_json)

#         return jsonify({
#             'message': f'Successfully generated {len(questions_data)} questions',
#             'questions': questions_data
#         })
#     except json.JSONDecodeError as e:
#         return jsonify({'error': f'Failed to parse AI response as JSON: {str(e)}'}), 400
#     except Exception as e:
#         print(f"Error generating MCQ preview: {e}")
#         return jsonify({'error': f'Failed to generate questions: {str(e)}'}), 500
